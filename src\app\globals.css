@tailwind base;
@tailwind components;
@tailwind utilities;

/* Japanese font support */
body {
  font-family: var(--font-noto-sans-jp), -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide{
  scrollbar-width: none;
}



/* Product description HTML styling */
.product-description {
  overflow: hidden;
}

.product-description strong {
  font-weight: 600;
}

.product-description p {
  margin-bottom: 0.5rem;
}

.product-description ol,
.product-description ul {
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.product-description ol {
  list-style-type: decimal;
}

.product-description ul {
  list-style-type: disc;
}

.product-description li {
  margin-bottom: 0.25rem;
}

.product-description em {
  font-style: italic;
}

/* Ensure line-clamp works with HTML content */
.line-clamp-2.product-description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}