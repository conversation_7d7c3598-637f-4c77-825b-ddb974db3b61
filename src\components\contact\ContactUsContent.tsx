"use client";

import { useState } from 'react';
import Image from 'next/image';
import { FiPhone, FiMail, FiMapPin, FiFacebook, FiInstagram, FiTwitter, FiLinkedin } from 'react-icons/fi';
import ContactForm from './ContactForm';

export default function ContactUsContent() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[400px] overflow-hidden bg-gradient-to-br from-main-color to-main-color/90">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="https://cocojojo.com/wp-content/uploads/2023/09/beauty-woman-with-pure-flower-min-scaled.jpg"
            alt="Contact COCOJOJO - Natural Beauty Support"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>

        {/* Content */}
        <div className="relative h-full flex items-center justify-center px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
          <div className="text-center text-white max-w-4xl">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fadeIn">
              お問い合わせ
              <span className="block text-light-green">COCOJOJO</span>
            </h1>
            <p className="text-lg md:text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed animate-fadeIn" style={{ animationDelay: '0.2s' }}>
              お客様からのご連絡をお待ちしております！製品のお問い合わせ、カスタマーサポート、ビジネス機会について、お気軽にチームにご連絡ください。
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-white/80 animate-fadeIn" style={{ animationDelay: '0.4s' }}>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                📞 カスタマーサポート
              </span>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                💬 製品のお問い合わせ
              </span>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                🤝 ビジネス機会
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-8">お気軽にお問い合わせください</h2>
              <p className="text-gray-600 mb-8 leading-relaxed">
                天然スキンケア製品についてご質問がございますか？ご注文のサポートが必要ですか？
                プライベートラベルサービスにご興味がございますか？お手伝いいたします！
              </p>
            </div>

            {/* Contact Details */}
            <div className="space-y-6">
              {/* Phone */}
              <div className="flex items-start gap-4 p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
                <div className="bg-main-color/10 p-3 rounded-full flex-shrink-0">
                  <FiPhone className="w-6 h-6 text-main-color" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">お電話でのお問い合わせ</h3>
                  <a
                    href="tel:+19496107164"
                    className="text-main-color hover:text-main-color/80 transition-colors text-lg font-medium"
                  >
                    (+1) ************
                  </a>
                  <p className="text-gray-600 text-sm mt-1">月曜日 - 金曜日、午前9:00 - 午後6:00 PST</p>
                </div>
              </div>

              {/* Email */}
              <div className="flex items-start gap-4 p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
                <div className="bg-main-color/10 p-3 rounded-full flex-shrink-0">
                  <FiMail className="w-6 h-6 text-main-color" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">メールアドレス</h3>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-main-color hover:text-main-color/80 transition-colors text-lg font-medium"
                  >
                    <EMAIL>
                  </a>
                  <p className="text-gray-600 text-sm mt-1">24時間以内にご返信いたします</p>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-start gap-4 p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
                <div className="bg-main-color/10 p-3 rounded-full flex-shrink-0">
                  <FiMapPin className="w-6 h-6 text-main-color" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">店舗所在地</h3>
                  <address className="text-gray-700 not-italic leading-relaxed">
                    3109 S Main St<br />
                    Santa Ana, CA 92707<br />
                    アメリカ合衆国
                  </address>
                  <a
                    href="https://maps.google.com/?q=3109+S+Main+St,+Santa+Ana,+CA+92707"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block mt-2 text-main-color hover:text-main-color/80 transition-colors text-sm font-medium"
                  >
                    Googleマップで見る →
                  </a>
                </div>
              </div>
            </div>

            {/* Social Media */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="font-semibold text-gray-900 mb-4">フォローしてください</h3>
              <div className="flex gap-4">
                <a
                  href="#"
                  className="bg-main-color/10 p-3 rounded-full hover:bg-main-color hover:text-white transition-colors text-main-color"
                  aria-label="Facebook"
                >
                  <FiFacebook className="w-5 h-5" />
                </a>
                <a
                  href="#"
                  className="bg-main-color/10 p-3 rounded-full hover:bg-main-color hover:text-white transition-colors text-main-color"
                  aria-label="Instagram"
                >
                  <FiInstagram className="w-5 h-5" />
                </a>
                <a
                  href="#"
                  className="bg-main-color/10 p-3 rounded-full hover:bg-main-color hover:text-white transition-colors text-main-color"
                  aria-label="Twitter"
                >
                  <FiTwitter className="w-5 h-5" />
                </a>
                <a
                  href="#"
                  className="bg-main-color/10 p-3 rounded-full hover:bg-main-color hover:text-white transition-colors text-main-color"
                  aria-label="LinkedIn"
                >
                  <FiLinkedin className="w-5 h-5" />
                </a>
              </div>
              <p className="text-gray-600 text-sm mt-4">
                Stay updated with our latest products, tips, and exclusive offers!
              </p>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-white rounded-xl shadow-sm border p-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Send Us a Message</h2>
              <p className="text-gray-600">
                Your email address will not be published. Required fields are marked <span className="text-red-500">*</span>
              </p>
            </div>
            <ContactForm />
          </div>
        </div>

        {/* Map Section */}
        <div className="mt-16">
          <div className="bg-white rounded-xl shadow-sm border overflow-hidden">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-900">Visit Our Store</h3>
              <p className="text-gray-600 mt-2">
                Come visit us at our Santa Ana location to experience our products firsthand.
              </p>
            </div>
            <div className="h-96 bg-gray-100 flex items-center justify-center">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3317.8234567890123!2d-117.8676543!3d33.7175!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80dcdbc123456789%3A0x123456789abcdef!2s3109%20S%20Main%20St%2C%20Santa%20Ana%2C%20CA%2092707!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="COCOJOJO Store Location"
              ></iframe>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
