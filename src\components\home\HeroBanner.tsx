"use client";

import Link from "next/link";
import Image from "next/image";
import { Fi<PERSON>rrowR<PERSON>, FiShoppingBag, FiPhone } from "react-icons/fi";

const HeroBanner = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-main-color via-main-color/90 to-main-color/80">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-48 -translate-y-48"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-48 translate-y-48"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-white rounded-full -translate-x-32 -translate-y-32"></div>
      </div>

      <div className="relative px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="text-white">
              <div className="mb-6">
                <span className="inline-block bg-white/20 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                  🎉 特別オファー - バルク注文最大30%オフ
                </span>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-6">
                  あなたの
                  <span className="block text-yellow-300">美容ブランドを立ち上げる準備はできていますか？</span>
                </h2>
                <p className="text-xl text-white/90 leading-relaxed mb-8">
                  コンセプトから店頭まで、以下を含む完全な製造ソリューションを提供します
                  contract manufacturing, private labeling, packaging, and filling services.
                </p>
              </div>

              {/* Features List */}
              <div className="grid sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full"></div>
                  <span className="text-white/90">Custom Formulation</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full"></div>
                  <span className="text-white/90">Private Labeling</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full"></div>
                  <span className="text-white/90">Premium Packaging</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full"></div>
                  <span className="text-white/90">Global Shipping</span>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/shop"
                  className="inline-flex items-center justify-center gap-3 bg-white text-main-color px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:scale-105 group"
                >
                  <FiShoppingBag size={20} />
                  <span>Browse Products</span>
                  <FiArrowRight className="group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center gap-3 border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-main-color transition-all duration-300 hover:scale-105 group"
                >
                  <FiPhone size={20} />
                  <span>Get Quote</span>
                  <FiArrowRight className="group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>

            {/* Right Image */}
            <div className="relative">
              <div className="relative h-96 md:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80"
                  alt="Professional cosmetic manufacturing and packaging"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-6 -left-6 bg-yellow-300 text-main-color rounded-xl p-4 shadow-lg">
                <div className="text-lg font-bold">GMP</div>
                <div className="text-xs">Certified</div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white text-main-color rounded-xl p-4 shadow-lg">
                <div className="text-lg font-bold">ISO</div>
                <div className="text-xs">Certified</div>
              </div>

              <div className="absolute top-1/2 -right-8 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                <div className="text-main-color text-lg font-bold">50+</div>
                <div className="text-main-color text-xs">Countries</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroBanner;
