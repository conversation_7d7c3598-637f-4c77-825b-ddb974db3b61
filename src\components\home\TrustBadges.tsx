"use client";

import { <PERSON>S<PERSON>eld, FiTruck, FiAward, FiHeart, FiGlobe, FiStar } from "react-icons/fi";
import { BiSolidCertification } from "react-icons/bi";
import { GiMedal, GiPlantRoots } from "react-icons/gi";
import { FaLeaf, FaSeedling } from "react-icons/fa";

const TrustBadges = () => {
  const badges = [
    {
      icon: BiSolidCertification,
      title: "GMP認証",
      description: "適正製造規範認証施設"
    },
    {
      icon: FiAward,
      title: "ISO認証",
      description: "国際品質基準準拠"
    },
    {
      icon: FaLeaf,
      title: "USDAオーガニック",
      description: "認証オーガニック成分とプロセス"
    },
    {
      icon: FiHeart,
      title: "クルエルティフリー",
      description: "動物実験なし、倫理的製造"
    },
    {
      icon: FiTruck,
      title: "迅速配送",
      description: "バルクおよび個別注文の迅速配送"
    },
    {
      icon: FiGlobe,
      title: "世界規模",
      description: "プレミアム品質で世界中のお客様にサービス提供"
    }
  ];

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            Trusted by Thousands Worldwide
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our certifications and commitments ensure you receive the highest quality 
            cosmetic products and services.
          </p>
        </div>

        {/* Badges Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 md:gap-8">
          {badges.map((badge, index) => {
            const Icon = badge.icon;
            return (
              <div
                key={index}
                className="group flex flex-col items-center text-center p-4 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1"
              >
                <div className="w-16 h-16 bg-main-color/10 rounded-full flex items-center justify-center mb-4 group-hover:bg-main-color/20 transition-colors duration-300">
                  <Icon className="text-main-color text-2xl group-hover:scale-110 transition-transform duration-300" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 text-sm md:text-base">
                  {badge.title}
                </h3>
                <p className="text-xs md:text-sm text-gray-600 leading-relaxed">
                  {badge.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="group">
            <div className="text-3xl md:text-4xl font-bold text-main-color mb-2 group-hover:scale-110 transition-transform duration-300">
              10K+
            </div>
            <div className="text-gray-600 text-sm md:text-base">Happy Customers</div>
          </div>
          <div className="group">
            <div className="text-3xl md:text-4xl font-bold text-main-color mb-2 group-hover:scale-110 transition-transform duration-300">
              500+
            </div>
            <div className="text-gray-600 text-sm md:text-base">Products</div>
          </div>
          <div className="group">
            <div className="text-3xl md:text-4xl font-bold text-main-color mb-2 group-hover:scale-110 transition-transform duration-300">
              50+
            </div>
            <div className="text-gray-600 text-sm md:text-base">Countries Served</div>
          </div>
          <div className="group">
            <div className="text-3xl md:text-4xl font-bold text-main-color mb-2 group-hover:scale-110 transition-transform duration-300">
              15+
            </div>
            <div className="text-gray-600 text-sm md:text-base">Years Experience</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrustBadges;
