import { MetadataRoute } from 'next'

// Types
interface MainCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
}

// Fetch data from API
async function fetchMainCategories(): Promise<MainCategory[]> {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    const response = await fetch(`${baseUrl}/api/shop/main-categories`, {
      next: { revalidate: 7200 } // 24 hours cache
    });

    if (!response.ok) {
      console.error('Failed to fetch main categories for sitemap');
      return [];
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching main categories for sitemap:', error);
    return [];
  }
}

async function fetchCategoriesForMainCategory(mainCategorySlug: string): Promise<Category[]> {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    const response = await fetch(`${baseUrl}/api/shop/main-categories/slug/${mainCategorySlug}/products`, {
      next: { revalidate: 7200 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Failed to fetch categories for ${mainCategorySlug}: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    return data.categories || [];
  } catch (error) {
    console.error(`Error fetching categories for ${mainCategorySlug}:`, error);
    return [];
  }
}

async function fetchProductsForCategory(categorySlug: string): Promise<Product[]> {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    const response = await fetch(`${baseUrl}/api/shop/categories/slug/${categorySlug}/products`, {
      next: { revalidate: 7200 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Failed to fetch products for ${categorySlug}: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error(`Error fetching products for ${categorySlug}:`, error);
    return [];
  }
}

async function fetchAllProducts(): Promise<Product[]> {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    const response = await fetch(`${baseUrl}/api/shop/products`, {
      next: { revalidate: 7200 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Failed to fetch all products: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching all products for sitemap:', error);
    return [];
  }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.VERCEL_URL||"www.cocojojo.com";
  const currentDate = new Date();
  
  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/shop`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/wholesale`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
  ];

  try {
    // Fetch main categories
    const mainCategories = await fetchMainCategories();
    
    // Main category pages
    const mainCategoryPages: MetadataRoute.Sitemap = mainCategories.map((category) => ({
      url: `${baseUrl}/shop/${category.slug}`,
      lastModified: currentDate,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }));

    // Category pages
    const categoryPages: MetadataRoute.Sitemap = [];

    for (const mainCategory of mainCategories) {
      // Fetch categories for this main category
      const categories = await fetchCategoriesForMainCategory(mainCategory.slug);

      for (const category of categories) {
        // Add category page
        categoryPages.push({
          url: `${baseUrl}/shop/${mainCategory.slug}/${category.slug}`,
          lastModified: currentDate,
          changeFrequency: 'weekly',
          priority: 0.7,
        });
      }
    }

    // Product pages - using new simplified structure
    const allProducts = await fetchAllProducts();
    const productPages: MetadataRoute.Sitemap = allProducts.map((product) => ({
      url: `${baseUrl}/product/${product.slug}`,
      lastModified: new Date(product.created_at),
      changeFrequency: 'daily',
      priority: 0.6,
    }));

    // Combine all pages
    return [
      ...staticPages,
      ...mainCategoryPages,
      ...categoryPages,
      ...productPages,
    ];

  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return at least static pages if dynamic content fails
    return staticPages;
  }
}

// Enable ISR for sitemap
export const revalidate = 600; // 24 hours
