"use client";

import Image from 'next/image';
import Link from 'next/link';
import { FiAward, FiHeart, FiStar, FiUsers, FiMail, FiArrowRight } from 'react-icons/fi';

export default function AboutUsContent() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-[500px] overflow-hidden bg-gradient-to-br from-main-color to-main-color/90">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="https://cocojojo.com/wp-content/uploads/2023/09/beauty-woman-with-pure-flower-min-scaled.jpg"
            alt="Beautiful woman with pure flower - COCOJOJO natural beauty"
            fill
            className="object-cover opacity-20"
            priority
          />
        </div>

        {/* Content */}
        <div className="relative h-full flex items-center justify-center px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
          <div className="text-center text-white max-w-4xl">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fadeIn">
              私たちについて
              <span className="block text-light-green">COCOJOJO</span>
            </h1>
            <p className="text-lg md:text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed animate-fadeIn" style={{ animationDelay: '0.2s' }}>
              2009年以来、天然美容への情熱を持ち続けています。お肌を育み、環境を尊重するUSDA認証オーガニック製品を製造しています。
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-white/80 animate-fadeIn" style={{ animationDelay: '0.4s' }}>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                🌿 USDA認証
              </span>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                ✨ 2009年設立
              </span>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                💚 天然・オーガニック
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16">
        {/* Our Story Section */}
        <section className="mb-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">私たちのストーリー</h2>
              <div className="space-y-6 text-gray-700 leading-relaxed">
                <p>
                  COCOJOJOでは、一般的な美容ルーティンを超越したスキンケア、ヘアケア、ボディケアへの揺るぎない情熱に突き動かされています。業界への深い愛に触発され、卓越性を体現し、個性を育むブランドを創造する変革的な旅に乗り出しました。
                </p>
                <p>
                  私たちのビジョンは重要な瞬間と挑戦によって形作られ、品質、コンプライアンス、イノベーションへの揺るぎないコミットメントを築き上げました。業界での長年の専門知識と経験により、最高の業界基準を遵守し、効果的であるだけでなく革新的な製品を一貫して製造することに大きな誇りを持っています。
                </p>
                <p>
                  継続的な研究開発への献身が、美容の分野における先駆者として私たちを際立たせています。
                </p>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="relative h-96 rounded-2xl overflow-hidden shadow-xl">
                <Image
                  src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&auto=format&fit=crop"
                  alt="Natural skincare laboratory and ingredients"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Certification Section */}
        <section className="mb-20">
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="relative h-80 rounded-xl overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1596755389378-c31d21fd1273?w=800&auto=format&fit=crop"
                    alt="USDA certified organic products"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div>
                <div className="flex items-center gap-3 mb-6">
                  <div className="bg-main-color/10 p-3 rounded-full">
                    <FiAward className="w-6 h-6 text-main-color" />
                  </div>
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900">USDA認証の卓越性</h2>
                </div>
                <div className="space-y-6 text-gray-700 leading-relaxed">
                  <p>
                    2009年、私たちはUSDA認証製品を提供するという重要な決断を下しました。これは、お客様と環境の健康に対する私たちの真の関心の証です。
                  </p>
                  <p>
                    USDAとCCOFのシールを誇らしく掲げる私たちの認証は、信頼の象徴として、天然・オーガニック製品の提供をお約束します。このコミットメントにより、すべての製品がオーガニックの完全性に関する最も厳しい基準を満たすことが保証されます。
                  </p>
                </div>
                <div className="mt-8 flex flex-wrap gap-4">
                  <div className="bg-green-50 border border-green-200 px-4 py-2 rounded-lg">
                    <span className="text-green-800 font-medium">USDAオーガニック</span>
                  </div>
                  <div className="bg-green-50 border border-green-200 px-4 py-2 rounded-lg">
                    <span className="text-green-800 font-medium">CCOF認証</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">私たちの価値観</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              COCOJOJOで私たちが行うすべてを導く原則
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
              <div className="bg-main-color/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiStar className="w-8 h-8 text-main-color" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">天然・オーガニック</h3>
              <p className="text-gray-600">最高品質の天然・オーガニック成分のみの使用にコミット</p>
            </div>
            <div className="text-center p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
              <div className="bg-main-color/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiAward className="w-8 h-8 text-main-color" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">品質の卓越性</h3>
              <p className="text-gray-600">製造するすべての製品で最高の業界基準を遵守</p>
            </div>
            <div className="text-center p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
              <div className="bg-main-color/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiHeart className="w-8 h-8 text-main-color" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">顧客ケア</h3>
              <p className="text-gray-600">お客様独自のニーズを理解し、完全な満足を保証</p>
            </div>
            <div className="text-center p-6 bg-white rounded-xl shadow-sm border hover:shadow-md transition-shadow">
              <div className="bg-main-color/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiUsers className="w-8 h-8 text-main-color" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">エンパワーメント</h3>
              <p className="text-gray-600">プライベートラベルサービスを通じて起業家をサポート</p>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="mb-20">
          <div className="bg-gradient-to-r from-main-color to-main-color/90 rounded-2xl p-8 md:p-12 text-white">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">あなたの旅をエンパワー</h2>
                <div className="space-y-6 leading-relaxed">
                  <p>
                    私たちのブランドの核心は、旅を受け入れることにあります - 私たちの旅、あなたの旅、そして志高い起業家の旅を。私たちの旅は信じられないほど素晴らしいものでした。そして、他の人々が成功への道のりを歩み始めることをエンパワーすることを信じています。
                  </p>
                  <p>
                    COCOJOJOは、バルクプライベートラベル、充填・ラベリング、創造的にデザインされたラベルサービスなどの包括的なサービスを提供し、夢が花開くプラットフォームを提供しています。
                  </p>
                </div>
                <div className="mt-8 space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-light-green rounded-full"></div>
                    <span>バルクプライベートラベル</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-light-green rounded-full"></div>
                    <span>充填・ラベリングサービス</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-light-green rounded-full"></div>
                    <span>創造的ラベルデザイン</span>
                  </div>
                </div>
              </div>
              <div className="relative h-80 rounded-xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&auto=format&fit=crop"
                  alt="COCOJOJO manufacturing and labeling services"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Customer Satisfaction */}
        <section className="mb-20">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">お客様の満足、私たちの喜び</h2>
            <div className="max-w-4xl mx-auto space-y-6 text-gray-700 leading-relaxed">
              <p className="text-lg">
                私たちのブランドが繁栄する喜びを大切にしながら、私たちの究極の満足はお客様の満足から生まれます。COCOJOJOでは、お客様独自のニーズと願望を理解するために一歩踏み込み、各製品がお客様の本質と共鳴することを確実にします。
              </p>
              <p className="text-lg">
                喜んでいただいたお客様から輝かしいフィードバックをいただいており、これは卓越性への私たちのコミットメントの証です。
              </p>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center">
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              COCOJOJOの世界を探索
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              天然美容がその真の形を見つけるCOCOJOJOの洗練された世界を探索することをお招きします。COCOJOJOで、自然にあなたの美しさを高めてください。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/shop"
                className="inline-flex items-center gap-2 bg-main-color text-white px-8 py-4 rounded-lg hover:bg-main-color/90 transition-colors font-medium"
              >
                製品を見る
                <FiArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="/contact"
                className="inline-flex items-center gap-2 border-2 border-main-color text-main-color px-8 py-4 rounded-lg hover:bg-main-color hover:text-white transition-colors font-medium"
              >
                <FiMail className="w-5 h-5" />
                お問い合わせ
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
