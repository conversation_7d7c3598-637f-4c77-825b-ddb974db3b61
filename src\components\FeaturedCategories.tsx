"use client";

import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { useEffect, useRef } from "react";

// Define the structure for featured category data
interface FeaturedCategory {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  slug: string;
  bgColor: string;
}

// Featured categories data with the provided descriptions
const featuredCategories: FeaturedCategory[] = [
  {
    id: "packaging",
    title: "パッケージング",
    description: "ご安心ください。私たちの製品は、お客様の商品の完全性を保持するよう設計されています。自信を持ってパッケージングを始めましょう！プレミアム素材で作られた化粧品容器は、企業にとって高級感と信頼性を醸し出します。医薬品、ヘルスケア、化粧品、食品・飲料、家庭用品、商業部門にわたって包括的なパッケージングサービスを提供しています。",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2024/02/cocojojo-packaging-banner-1.png",
    slug: "packaging",
    bgColor: "bg-amber-50"
  },
  {
    id: "contract-manufacturing",
    title: "受託製造",
    description: "私たちの受託製造サービスは、あなたの美容ブランドのビジョンを実現するための科学主導のアプローチを提供します。GMP認証、ISO認証、USDAオーガニック認証工場に支えられ、処方全体を通じて最高の品質と安全基準を確保します。セミカスタマイゼーション、処方再現、カスタム処方を含む3つの異なるオプションで、成長中および確立されたブランドの多様なニーズに対応します。",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2023/09/Screenshot-2023-09-13-090551.png",
    slug: "contract-manufacturing",
    bgColor: "bg-blue-50"
  },
  {
    id: "labeling-filling",
    title: "ラベリング・充填サービス",
    description: "製品を研究室から店頭まで運ぶ、私たちのラベリング・充填サービスが成功への道のりを完成させます。GMP認証、ISO認証、USDAオーガニック認証工場により、プロセスの完全性を信頼していただけます。カスタム充填は、企業をボトリングの煩わしさから解放し、プレミアムプレゼンテーションのための標準的でユニークなパッケージングオプションを提供します。",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2023/09/Screenshot-2023-09-13-090617.png",
    slug: "labeling-filling",
    bgColor: "bg-green-50"
  },
  {
    id: "private-labeling",
    title: "プライベートラベル",
    description: "私たちのプライベートラベルサービスは、業界でプレミアムプレーヤーとしての地位を確立しようとするブランドのための強固な基盤を築きます。GMP認証、ISO認証、USDAオーガニック認証工場により、高品質な美容製品の独占ラインを自信を持って開発できます。倫理的で持続可能な実践に支えられた、私たちのクルエルティフリー製品は、意識の高い消費者に対応します。",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2023/08/AdobeStock_583313454.jpg",
    slug: "private-labeling",
    bgColor: "bg-rose-50"
  }
];

const FeaturedCategories = () => {
  // Create refs for each category section
  const categoryRefs = useRef<(HTMLDivElement | null)[]>([]);
  const headerRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  // Set up simple scroll animation
  useEffect(() => {
    // Function to check if an element is in viewport
    const isInViewport = (element: HTMLElement) => {
      const rect = element.getBoundingClientRect();
      return (
        rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.75 &&
        rect.bottom >= 0
      );
    };

    // Function to handle scroll
    const handleScroll = () => {
      // Animate header
      if (headerRef.current && isInViewport(headerRef.current)) {
        headerRef.current.style.opacity = '1';
        headerRef.current.style.transform = 'translateY(0)';
      }

      // Animate categories
      categoryRefs.current.forEach((ref) => {
        if (ref && isInViewport(ref)) {
          ref.style.opacity = '1';
          ref.style.transform = 'translateX(0)';
        }
      });

      // Animate CTA
      if (ctaRef.current && isInViewport(ctaRef.current)) {
        ctaRef.current.style.opacity = '1';
        ctaRef.current.style.transform = 'translateY(0)';
      }
    };

    // Initial check
    setTimeout(handleScroll, 100);

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className="py-16">
      {/* Header Section */}
      <div
        ref={headerRef}
        className="mb-16 text-center"
        style={{
          opacity: '0',
          transform: 'translateY(20px)',
          transition: 'opacity 0.8s ease, transform 0.8s ease'
        }}
      >
        <span className="text-main-color font-medium text-sm uppercase tracking-wider">What We Offer</span>
        <h2 className="text-3xl md:text-4xl font-medium text-gray-800 mt-2 mb-4">Our Premium Services</h2>
        <div className="w-24 h-1 bg-main-color mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Discover our comprehensive range of services designed to meet all your beauty and cosmetic product needs,
          from packaging to manufacturing and beyond.
        </p>
      </div>

      <div className="space-y-24">
        {featuredCategories.map((category, index) => {
          const isEven = index % 2 === 0;

          return (
            <div
              key={category.id}
              ref={el => { categoryRefs.current[index] = el; }}
              className={`${category.bgColor} rounded-2xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300`}
              style={{
                opacity: '0',
                transform: isEven ? 'translateX(50px)' : 'translateX(-50px)',
                transition: 'opacity 0.8s ease, transform 0.8s ease'
              }}
            >
              <div className={`flex flex-col ${isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center`}>
                {/* Image Section */}
                <div className="w-full lg:w-1/2 h-80 md:h-96 lg:h-[500px] relative overflow-hidden group">
                  <Image
                    src={category.imageUrl}
                    alt={category.title}
                    fill
                    priority={index < 2} // Load first two images with priority
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Content Section */}
                <div className="w-full lg:w-1/2 p-8 md:p-12 lg:p-16">
                  <h3 className="text-2xl md:text-3xl font-medium text-gray-800 mb-4">
                    {category.title}
                  </h3>
                  <div className="w-20 h-1 bg-main-color mb-6 transition-all duration-300 hover:w-32"></div>
                  <p className="text-gray-700 mb-8 leading-relaxed">
                    {category.description}
                  </p>
                  <Link
                    href={`/shop/${category.slug}`}
                    className="inline-flex items-center gap-2 bg-main-color hover:bg-main-color/90 text-white py-3 px-6 rounded-lg transition-all duration-300 hover:gap-3 hover:pl-7"
                  >
                    <span>{category.title}を探索</span>
                    <FiArrowRight className="transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Call to Action Section */}
      <div
        ref={ctaRef}
        className="mt-24 bg-main-color/10 rounded-2xl p-8 md:p-12 lg:p-16 text-center"
        style={{
          opacity: '0',
          transform: 'translateY(20px)',
          transition: 'opacity 0.8s ease, transform 0.8s ease'
        }}
      >
        <h3 className="text-2xl md:text-3xl font-medium text-gray-800 mb-4">始める準備はできていますか？</h3>
        <p className="text-gray-700 max-w-3xl mx-auto mb-8">
          パッケージングソリューション、受託製造、プライベートラベルサービスをお探しの場合、
          私たちの専門知識とプレミアム品質であなたのビジョンを実現するお手伝いをいたします。
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link
            href="/shop"
            className="bg-main-color hover:bg-main-color/90 text-white py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            すべての製品を探索
          </Link>
          <Link
            href="/contact"
            className="bg-white border border-main-color text-main-color hover:bg-main-color/5 py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            お問い合わせ
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FeaturedCategories;
