import type { Metadata } from 'next';
import AboutUsContent from '@/components/about/AboutUsContent';

export const metadata: Metadata = {
  title: '会社概要 - 私たちのストーリー・ミッション',
  description: 'COCOJOJOの歩み、天然スキンケアへの情熱、2009年以来の品質USDA認証オーガニック製品へのコミットメントについて学んでください。',
  keywords: 'cocojojo 会社概要, 天然スキンケア会社, USDA認証オーガニック, 美容ブランドストーリー, オーガニック化粧品, 日本',
  openGraph: {
    title: 'COCOJOJO について - 2009年以来の天然美容の卓越性',
    description: '天然スキンケアへの情熱と品質USDA認証オーガニック製品へのコミットメントをご覧ください。',
    type: 'website',
    url: '/about',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'COCOJOJO について - 2009年以来の天然美容の卓越性',
    description: '天然スキンケアへの情熱と品質USDA認証オーガニック製品へのコミットメントをご覧ください。',
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <AboutUsContent />
    </div>
  );
}
