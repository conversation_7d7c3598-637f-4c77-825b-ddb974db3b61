import type { Metadata } from "next";
import { Inter, Noto_Sans_JP } from "next/font/google";
import "./globals.css";
import LayoutWrapper from "@/components/LayoutWrapper";

const inter = Inter({ subsets: ["latin"] });
const notoSansJP = Noto_Sans_JP({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-noto-sans-jp"
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NODE_ENV === 'production'
    ? process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'https://cocojojo.com'
    : 'http://localhost:3000'
  ),
  title: {
    template: "%s | CocoJojo Japan",
    default: "CocoJojo Japan - プレミアムオーガニック美容製品・化粧品製造",
  },
  description: "CocoJojo Japanは、日本市場向けの最高品質オーガニック美容製品と化粧品製造サービスを提供します。GMP認証・ISO認証・USDAオーガニック認証工場で製造された100%天然成分の化粧品、受託製造、プライベートラベル、パッケージングサービスをご利用ください。",
  keywords: "オーガニック化粧品 日本, 美容製品 製造, スキンケア 日本, 天然成分 化粧品, 受託製造 日本, プライベートラベル 化粧品, 化粧品 OEM, オーガニック認証, GMP認証 化粧品, 卸売 美容製品, パッケージング サービス, 日本 化粧品 輸入",
  authors: [{ name: "CocoJojo Japan チーム" }],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: process.env.VERCEL_URL,
    languages: {
      'ja-JP': process.env.VERCEL_URL,
      'ja': process.env.VERCEL_URL,
    },
  },
  openGraph: {
    type: "website",
    locale: "ja_JP",
    url: process.env.VERCEL_URL,
    siteName: "CocoJojo Japan",
    title: "CocoJojo Japan - プレミアムオーガニック美容製品・化粧品製造",
    description: "日本市場向けの最高品質オーガニック美容製品と化粧品製造サービス。GMP・ISO・USDAオーガニック認証工場で製造。",
    images: [
      {
        url: "/images/cocojojo-og-jp.jpg",
        width: 1200,
        height: 630,
        alt: "CocoJojo Japan - プレミアムオーガニック化粧品",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "CocoJojo Japan - プレミアムオーガニック美容製品",
    description: "日本市場向けの最高品質オーガニック美容製品と化粧品製造サービス。",
    images: ["/images/cocojojo-og-jp.jpg"],
  },
  verification: {
    google: "your-google-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "CocoJojo Japan",
    "alternateName": "CocoJojo",
    "url": process.env.VERCEL_URL || "https://cocojojo.com",
    "logo": `${process.env.VERCEL_URL || "https://cocojojo.com"}/images/cocojojo-logo.png`,
    "description": "日本市場向けの最高品質オーガニック美容製品と化粧品製造サービスを提供する企業",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "3109 S Main St",
      "addressLocality": "Santa Ana",
      "addressRegion": "CA",
      "postalCode": "92707",
      "addressCountry": "US"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-610-7164",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["Japanese", "English"]
    },
    "sameAs": [
      "https://www.facebook.com/cocojojo",
      "https://www.instagram.com/cocojojo",
      "https://www.youtube.com/cocojojo"
    ],
    "foundingDate": "1997",
    "numberOfEmployees": "50-100",
    "industry": "化粧品製造・美容製品",
    "keywords": "オーガニック化粧品, 美容製品製造, 受託製造, プライベートラベル",
    "areaServed": {
      "@type": "Country",
      "name": "Japan"
    }
  };

  return (
    <html lang="ja">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
        <link rel="alternate" hrefLang="ja" href={process.env.VERCEL_URL || "https://cocojojo.com"} />
        <link rel="alternate" hrefLang="ja-JP" href={process.env.VERCEL_URL || "https://cocojojo.com"} />
        <link rel="alternate" hrefLang="x-default" href={process.env.VERCEL_URL || "https://cocojojo.com"} />
      </head>
      <body className={`${inter.className} ${notoSansJP.variable}`}>
        <LayoutWrapper>
          {children}
        </LayoutWrapper>
      </body>
    </html>
  );
}
