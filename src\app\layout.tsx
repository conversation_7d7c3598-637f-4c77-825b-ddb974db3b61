import type { Metadata } from "next";
import { Inter, Noto_Sans_JP } from "next/font/google";
import "./globals.css";
import LayoutWrapper from "@/components/LayoutWrapper";

const inter = Inter({ subsets: ["latin"] });
const notoSansJP = Noto_Sans_JP({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-noto-sans-jp"
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NODE_ENV === 'production'
    ? process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'https://cocojojo.com'
    : 'http://localhost:3000'
  ),
  title: {
    template: "%s | CocoJojo",
    default: "CocoJojo - プレミアムオーガニック美容製品",
  },
  description: "CocoJojoでは、純粋で自然な成分を使用した高品質な美容製品を信じています。ウィッチヘーゼルローズトナーの治癒力を活用し、本物の結果をもたらし、あなたの健康を向上させ、地球を持続させる正直な美容製品をお届けします。添加物なし、余計なものなし。100%純粋なオーガニックオイルのみ。",
  keywords: "オーガニックオイル, 美容製品, スキンケア, 天然成分, ウィッチヘーゼル, ローズトナー, 純粋オイル, 日本, 化粧品",
  authors: [{ name: "CocoJojoチーム" }],
  openGraph: {
    type: "website",
    locale: "ja_JP",
    url: process.env.VERCEL_URL,
    siteName: "CocoJojo",
    title: "CocoJojo - プレミアムオーガニック美容製品",
    description: "純粋で自然な成分を使用した高品質な美容製品で、本物の結果と健康をお届けします。",
  },
  twitter: {
    card: "summary_large_image",
    title: "CocoJojo - プレミアムオーガニック美容製品",
    description: "純粋で自然な成分を使用した高品質な美容製品で、本物の結果と健康をお届けします。",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ja">
      <body className={`${inter.className} ${notoSansJP.variable}`}>
        <LayoutWrapper>
          {children}
        </LayoutWrapper>
      </body>
    </html>
  );
}
